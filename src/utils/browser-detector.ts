// src/utils/browser-detector.ts

export function detectBrowser(): string {
  const userAgent = navigator.userAgent;

  if (userAgent.includes("Chrome") && !userAgent.includes("Chromium")) {
    return "Chrome";
  }
  if (userAgent.includes("Firefox")) {
    return "Firefox";
  }
  if (userAgent.includes("Edg")) {
    return "Edge";
  }
  
  if (userAgent.includes("Safari") && !userAgent.includes("Chrome")) {
    return "Safari";
  }
  // Add more browser detections as needed
  return "Unknown";
}

export function isYtDlpCookieSupported(browser: string): boolean {
  // For simplicity, assume Chrome and Firefox support yt-dlp cookie selection automatically
  // In a real-world scenario, this might involve more complex checks or a list of supported browsers.
  return browser === "Chrome" || browser === "Firefox";
}