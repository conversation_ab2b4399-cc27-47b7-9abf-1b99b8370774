import type { IVideo } from '../interfaces/video';
import type { IVideoClip } from '../interfaces/clip-time';
import { secondToTimeString } from './second-to-time-string';
import { detectBrowser, isYtDlpCookieSupported } from './browser-detector';

export interface CommandOptions {
	includeMetadata?: boolean;
	includeSubtitles?: boolean;
	writeAutoSubs?: boolean;
	outputTemplate?: string;
	quality?: string;
	format?: string;
	delogo?: { x: number; y: number; width: number; height: number; show?: boolean };
	browserCookies?: string; // 新增用于手动指定浏览器cookie的参数
}



/**
 * 生成单个片段的yt-dlp命令
 */
export function generateSingleClipCommand(
	videoId: string,
	clip: IVideoClip,
	options: CommandOptions = {}
): string {
	const {
		includeMetadata = true,
		includeSubtitles = false,
		writeAutoSubs = false,
		outputTemplate,
		quality = 'best',
		delogo,
		browserCookies // 获取新增的browserCookies参数
	} = options;

	const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
	const startTime = secondToTimeString(clip.start);
	const endTime = secondToTimeString(clip.end);
	const duration = Math.round(clip.end - clip.start);
	
	// 生成文件名
	const clipName = clip.name || `clip_${startTime.replace(/:/g, '-')}`;
	const currentDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');
	
	const commandArgs: string[] = [`yt-dlp`, `"${videoUrl}"`];
	
	// 添加片段下载参数
	commandArgs.push(`--download-sections`, `"*${startTime}-${endTime}"`);
	
	// 添加输出模板
	const template = outputTemplate || `${clipName}_${duration}s_${currentDate}_%(id)s_%(uploader)s.%(ext)s`;
	commandArgs.push(`--output`, `"${template}"`);
	
	// 添加质量设置
	if (quality === 'best_compatible') {
		commandArgs.push(`-S`, `"vcodec:h264,ext:mp4"`);  // 最大兼容模式
	} else {
		commandArgs.push(`-S`, `"vcodec:av01,ext:mp4"`); // 最佳质量模式
	}
	
	// 添加delogo滤镜
	if (delogo) {
		const { x, y, width, height, show = false } = delogo;
		commandArgs.push(`--postprocessor-args`, `"-vf delogo=x=${x}:y=${y}:w=${width}:h=${height}:show=${show ? 1 : 0}"`);
	}
	
	// 添加浏览器cookies
	if (browserCookies) {
		// 如果手动指定了浏览器cookie，则使用手动指定的
		commandArgs.push(`--cookies-from-browser`, browserCookies);
	} else {
		// 否则，尝试自动检测浏览器并生成cookie参数
		const browser = detectBrowser();
		if (isYtDlpCookieSupported(browser)) {
			commandArgs.push(`--cookies-from-browser`, browser.toLowerCase());
		} else {
			// 如果浏览器不支持自动获取cookie，可以添加警告或提示用户手动选择
			// 这里暂时不添加任何参数，表示需要手动选择
		}
	}
	
	// 添加元数据和字幕
	if (includeMetadata) {
		commandArgs.push(`--embed-metadata`);
	}
	if (includeSubtitles) {
		if (writeAutoSubs) {
			commandArgs.push(`--write-auto-subs`);
		} else {
			commandArgs.push(`--embed-subs`);
		}
	}
	
	
	return commandArgs.join(' ');
}

/**
 * 生成多个片段���批量yt-dlp命令
 */
export function generateBatchClipsCommand(
	video: IVideo,
	options: CommandOptions = {}
): string {
	const {
		includeMetadata = true,
		includeSubtitles = false,
		writeAutoSubs = false,
		quality = 'best',
		format = 'mp4',
	
	} = options;

	if (!video.clips || video.clips.length === 0) {
		throw new Error('视频没有片段数据');
	}

	const videoUrl = `https://www.youtube.com/watch?v=${video.id}`;
	const currentDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');
	
	const commandArgs: string[] = [`yt-dlp`, `"${videoUrl}"`];
	
	// 添加所有片段的下载参数
	video.clips.forEach(clip => {
		const startTime = secondToTimeString(clip.start);
		const endTime = secondToTimeString(clip.end);
		commandArgs.push(`--download-sections`, `"*${startTime}-${endTime}"`);
	});
	
	// 添加输出模板（使用section_title变量）
	const template = `%(section_title)s_%(section_duration)ss_${currentDate}_%(id)s_%(uploader)s.%(ext)s`;
	commandArgs.push(`--output`, `"${template}"`);
	
	// 添加质量设置
	if (quality === 'best_compatible' && format === 'mp4') { // 修改条件判断
		commandArgs.push(`--format`, `"best_compatible[ext=${format}]"`); // 正确生成 best_compatible[ext=mp4]
	} else if (quality === 'best' && format === 'mp4') {
		commandArgs.push(`-S`, `"vcodec:h264,ext:mp4"`);
	} else {
		commandArgs.push(`--format`, `"${quality}[ext=${format}]"`);
	}
	
	// 添加元数据和字幕
	if (includeMetadata) {
		commandArgs.push(`--embed-metadata`);
	}
	if (includeSubtitles) {
		if (writeAutoSubs) {
			commandArgs.push(`--write-auto-subs`);
		} else {
			commandArgs.push(`--embed-subs`);
		}
	}
	
	return commandArgs.join(' ');
}



/**
 * 生成智能文件命名模板
 */
export function generateNamingTemplate(
	clipName: string,
	includeMetadata: boolean = true
): string {
	const currentDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');
	
	let template = `${clipName}_%(section_duration)ss_${currentDate}`;
	
	if (includeMetadata) {
		template += '_%(id)s_%(upload_date)s_%(uploader)s_%(view_count)s';
	} else {
		template += '_%(id)s';
	}
	
	template += '.%(ext)s';
	
	return template;
}

/**
 * 解析和验证yt-dlp命令
 */
export function validateCommand(command: string): {
	isValid: boolean;
	errors: string[];
	warnings: string[];
} {
	const errors: string[] = [];
	const warnings: string[] = [];
	
	// 基础验证
	if (!command.includes('yt-dlp')) {
		errors.push('命令必须包含 yt-dlp');
	}
	
	if (!command.includes('youtube.com/watch')) {
		errors.push('必须包含有效的YouTube视频URL');
	}
	
	if (!command.includes('--download-sections')) {
		warnings.push('未指定下载片段，将下载整个视频');
	}
	
	if (!command.includes('--output')) {
		warnings.push('未指定输出文件名模板');
	}
	
	return {
		isValid: errors.length === 0,
		errors,
		warnings
	};
}