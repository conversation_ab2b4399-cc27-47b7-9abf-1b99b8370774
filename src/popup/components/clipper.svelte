<script lang="ts">
	import { onMount } from 'svelte';
	import { secondToTimeString } from '../../utils/second-to-time-string';
	import { timeStringToSecond } from '../../utils/time-string-to-second';
	import { storage } from '../stores/storage';
	import ClipTime from './clip-time.svelte';
	import type { IClipTime } from '../interfaces/clip-time';
	import type { IVideoClip } from '../../interfaces/clip-time';
	import CloseButton from './close-button.svelte';
	import AddButton from './add-button.svelte';
	import PreviewModal from './preview-modal.svelte';
	import type { IPreviewModalConfig } from '../../interfaces/video-preview';
	import BoundingBoxSelector from './BoundingBoxSelector.svelte'; // 确保导入

	export let tab: chrome.tabs.Tab;
	export let id: string;
	let loop: boolean = false;

	// 视频预览相关状态
	let showPreviewModal = false;
	let previewModalConfig: IPreviewModalConfig;
	let selectedRegion = { x: 0, y: 0, width: 0, height: 0 }; // 用于存储边界框选择的区域

	let clips: IClipTime[] = [
		{
			start: '',
			end: '',
			name: '',
		},
	];

	let message = '';

	$: isCanSave = !!clips.find((clip) => clip.start || clip.end || clip.name.trim() || loop);

	onMount(async () => {
		// 新的工作流程：剪藏页面始终显示空的输入框
		// 已保存的片段只在"记录"页面显示
		// 只保留loop状态的加载
		const video = $storage.videos[id];
		if (video) {
			loop = video.loop;
		}

		// 初始化预览模态配置
		previewModalConfig = {
			videoId: id,
			tabId: tab.id!,
			// 初始时传递空的selectedRegion，或者根据需要从存储加载
			selectedRegion: { x: 0, y: 0, width: 0, height: 0 }
		};
	});

	async function saveVideo() {
		const videoClips: IVideoClip[] = [];
		for (let idx = 0; idx < clips.length; idx++) {
			const clip = clips[idx];
			const canSave = clip.start || clip.end || clip.name.trim() || loop;
			if (!canSave) {
				continue;
			}
			const startSeconds = timeStringToSecond(clip.start);
			if (clip.start && startSeconds === -1) {
				message = 'Start time is not valid';
				return;
			}

			const endSeconds = timeStringToSecond(clip.end);
			if (clip.end && endSeconds === -1) {
				message = 'End time is not valid';
				return;
			}

			if (clip.start && clip.end && endSeconds <= startSeconds) {
				message = 'End time should be greater than start time';
				return;
			}

			if (idx > 0) {
				if (startSeconds <= videoClips[idx - 1].end || videoClips[idx - 1].end === -1) {
					message = `Start must be greater than the end of prev clip`;
					return;
				}
			}

			videoClips.push({
				start: startSeconds,
				end: endSeconds,
				name: clip.name.trim() || `片段${idx + 1}`,  // 如果没有名称，使用默认名称
			});
		}

		storage.update((prev) => {
			prev.lastSync = new Date().getTime();

			// 如果视频已存在，进行去重合并
			if (prev.videos[id]) {
				const existingClips = prev.videos[id].clips;
				const mergedClips = [...existingClips];

				// 对新片段进行去重检查（只有开始和结束时间完全相同才视为重复）
				for (const newClip of videoClips) {
					const isDuplicate = existingClips.some(existingClip =>
						existingClip.start === newClip.start &&
						existingClip.end === newClip.end
					);

					if (!isDuplicate) {
						mergedClips.push(newClip);
					}
				}

				// 按开始时间排序
				mergedClips.sort((a, b) => a.start - b.start);

				prev.videos[id] = {
					...prev.videos[id],
					clips: mergedClips,
					loop: loop,
				};
			} else {
				// 新视频，直接保存
				prev.videos[id] = {
					id,
					title: tab.title!,
					clips: videoClips,
					loop: loop,
				};
			}
			return prev;
		});

		// 保存成功后清空所有输入框，等待下一次输入
		clearUnsavedClips();
		message = 'Saved';
	}

	// 修改为只清除未保存的片段输入，不删除已保存的视频记录
	function clearUnsavedClips() {
		// 重置所有片段输入框为空
		for (let i = 0; i < clips.length; i++) {
			clips[i].start = '';
			clips[i].end = '';
			clips[i].name = '';
		}
		// 重置为只有一个空片段
		clips = [{
			start: '',
			end: '',
			name: '',
		}];
		message = 'Cleared unsaved clips';
	}

	function updateClips(isInc: boolean) {
		if (isInc) {
			clips.push({
				end: '',
				start: '',
				name: '',
			});
			clips = [...clips];
			return;
		}
		clips.pop();
		clips = [...clips];
	}

	/**
	 * 打开视频预览模态框
	 */
	function openPreviewModal() {
		// 传递当前选择区域给预览模态框
		previewModalConfig = {
			...previewModalConfig,
			selectedRegion: selectedRegion
		};
		showPreviewModal = true;
	}

	/**
	 * 关闭视频预览模态框
	 */
	function closePreviewModal() {
		showPreviewModal = false;
	}

	/**
	 * 处理预览错误
	 */
	function handlePreviewError(event: CustomEvent) {
		const errorMessage = event.detail as string;
		message = `预览错误: ${errorMessage}`;
		console.error('视频预览错误:', errorMessage);
	}

	/**
	 * 处理边界框选择器区域变化
	 */
	function handleRegionChange(event: CustomEvent) {
		selectedRegion = event.detail;
		message = `区域选择: x=${selectedRegion.x}, y=${selectedRegion.y}, w=${selectedRegion.width}, h=${selectedRegion.height}`;
	}

	/**
	 * 处理边界框选择器重置
	 */
	function handleRegionReset() {
		selectedRegion = { x: 0, y: 0, width: 0, height: 0 };
		message = '区域选择已重置';
	}

	/**
	 * 处理预览模态框确认（现在只用于获取delogo区域）
	 */
	function handlePreviewConfirm(event: CustomEvent) {
		const delogoRegion = event.detail;
		selectedRegion = delogoRegion; // 将预览模态框返回的区域设置为当前选择区域
		message = `Delogo区域已确认: x=${selectedRegion.x}, y=${selectedRegion.y}, w=${selectedRegion.width}, h=${selectedRegion.height}`;
	}
</script>

<div class="w-full">
	<div class="flex gap-2 items-start">
		<img width="20px" src={tab.favIconUrl} alt="icon" />
		<span class="text-sm font-medium">{tab.title}</span>
	</div>
	<div class="flex justify-between items-center mt-4">
		<div class="flex gap-2 items-center">
			<button disabled={!isCanSave} on:click={saveVideo} class="font-semibold bg-color0 py-1 px-3 rounded-md disabled:opacity-80 disabled:cursor-not-allowed">Save</button>
			<button on:click={clearUnsavedClips} class="text-dark font-semibold bg-yellow-400 py-1 px-3 rounded-md hover:bg-yellow-500" title="清除未保存的片段输入">Clear</button>
			<button on:click={openPreviewModal} class="font-semibold bg-blue-600 text-white py-1 px-3 rounded-md hover:bg-blue-700" title="视频预览与区域选择">
				<svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
				</svg>
				Preview
			</button>
			{#if message != ''}
				<p class="ml-2 text-green-500 font-medium text-xs">{message}</p>
			{/if}
		</div>
		<div class="flex justify-center items-start gap-2">
			<input
				bind:checked={loop}
				id="checked-checkbox"
				type="checkbox"
				value=""
				class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
			/>
			<label for="checked-checkbox" class="text-xs font-medium text-gray-900 dark:text-gray-300">LOOP</label>
		</div>
	</div>
	<div class="flex flex-col mb-1 mt-2 gap-2 items-start">
		{#each clips as clip}
			<div class="flex items-center gap-5 w-full">
				<ClipTime bind:clip />
				<CloseButton hide={clips.length < 2} onClick={() => updateClips(false)} />
			</div>
		{/each}
		<div class="w-[70px] rounded-md">
			<AddButton onClick={() => updateClips(true)} />
		</div>
	</div>

	<!-- 边界框选择器 -->
	<div class="mt-4 p-4 border rounded-md bg-gray-50 dark:bg-gray-800">
		<h3 class="text-md font-semibold mb-2 text-gray-800 dark:text-gray-200">Delogo 区域选择</h3>
		<BoundingBoxSelector
			bind:region={selectedRegion}
			on:change={handleRegionChange}
			on:reset={handleRegionReset}
		/>
		<p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
			当前区域: x={selectedRegion.x}, y={selectedRegion.y}, width={selectedRegion.width}, height={selectedRegion.height}
		</p>
	</div>
</div>

<!-- 视频预览模态框 -->
{#if showPreviewModal}
	<PreviewModal
		bind:isOpen={showPreviewModal}
		config={previewModalConfig}
		onClose={closePreviewModal}
		on:confirm={handlePreviewConfirm}
		on:error={handlePreviewError}
	/>
{/if}